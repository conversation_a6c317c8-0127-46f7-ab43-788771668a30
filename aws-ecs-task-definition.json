{"family": "dld-etl-pipeline", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "2048", "memory": "4096", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/dld-etl-task-role", "containerDefinitions": [{"name": "dld-etl-container", "image": "ACCOUNT_ID.dkr.ecr.REGION.amazonaws.com/dld-etl:latest", "essential": true, "portMappings": [{"containerPort": 8080, "protocol": "tcp"}], "environment": [{"name": "CLOUD_PROVIDER", "value": "aws"}, {"name": "AWS_REGION", "value": "us-east-1"}, {"name": "S3_BUCKET", "value": "dld-data-bucket"}, {"name": "RAW_DATA_DIR", "value": "s3://dld-data-bucket/raw"}, {"name": "PROCESSED_DATA_DIR", "value": "s3://dld-data-bucket/processed"}, {"name": "LOG_LEVEL", "value": "INFO"}, {"name": "BATCH_SIZE", "value": "1000"}, {"name": "PARALLEL_PROCESSING", "value": "true"}, {"name": "MAX_RETRIES", "value": "3"}, {"name": "ENABLE_METRICS", "value": "true"}, {"name": "METRICS_PORT", "value": "8080"}], "secrets": [{"name": "DLD_API_CONSUMER_ID", "valueFrom": "arn:aws:secretsmanager:REGION:ACCOUNT_ID:secret:dld-api-keys:api-consumer-id::"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/dld-etl-pipeline", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8080/health || exit 1"], "interval": 30, "timeout": 10, "retries": 3, "startPeriod": 60}, "stopTimeout": 120, "ulimits": [{"name": "nofile", "softLimit": 65536, "hardLimit": 65536}]}], "volumes": [], "placementConstraints": [], "tags": [{"key": "Environment", "value": "production"}, {"key": "Application", "value": "dld-etl"}, {"key": "Version", "value": "1.0.0"}]}