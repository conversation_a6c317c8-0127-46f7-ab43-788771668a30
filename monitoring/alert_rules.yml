# Prometheus Alert Rules for Dubai Real Estate ETL Platform

groups:
  - name: dld_etl_alerts
    rules:
      # Service availability alerts
      - alert: DLDETLServiceDown
        expr: dld_etl_up == 0
        for: 1m
        labels:
          severity: critical
          service: dld-etl
        annotations:
          summary: "DLD ETL service is down"
          description: "The DLD ETL service has been down for more than 1 minute."

      - alert: DLDETLHighCPUUsage
        expr: dld_etl_cpu_percent > 80
        for: 5m
        labels:
          severity: warning
          service: dld-etl
        annotations:
          summary: "High CPU usage detected"
          description: "CPU usage is above 80% for more than 5 minutes. Current value: {{ $value }}%"

      - alert: DLDETLHighMemoryUsage
        expr: dld_etl_memory_percent > 85
        for: 5m
        labels:
          severity: warning
          service: dld-etl
        annotations:
          summary: "High memory usage detected"
          description: "Memory usage is above 85% for more than 5 minutes. Current value: {{ $value }}%"

      # ETL pipeline alerts
      - alert: DLDETLHighFailureRate
        expr: (rate(dld_etl_requests_failed_total[5m]) / rate(dld_etl_requests_total[5m])) > 0.1
        for: 2m
        labels:
          severity: warning
          service: dld-etl
        annotations:
          summary: "High ETL failure rate"
          description: "ETL failure rate is above 10% for the last 5 minutes. Current rate: {{ $value | humanizePercentage }}"

      - alert: DLDETLNoRecentData
        expr: time() - dld_etl_last_successful_run_timestamp > 86400
        for: 0m
        labels:
          severity: critical
          service: dld-etl
        annotations:
          summary: "No recent ETL data"
          description: "No successful ETL run in the last 24 hours. Last run: {{ $value | humanizeTimestamp }}"

      # Data quality alerts
      - alert: DLDETLDataSizeAnomaly
        expr: abs(dld_etl_data_size_mb - avg_over_time(dld_etl_data_size_mb[7d])) > (2 * stddev_over_time(dld_etl_data_size_mb[7d]))
        for: 5m
        labels:
          severity: warning
          service: dld-etl
        annotations:
          summary: "Data size anomaly detected"
          description: "Current data size ({{ $value }}MB) deviates significantly from the 7-day average."

      - alert: DLDETLLowDataFileCount
        expr: dld_etl_data_files_count < 2
        for: 5m
        labels:
          severity: warning
          service: dld-etl
        annotations:
          summary: "Low data file count"
          description: "Only {{ $value }} data files found. Expected at least 2 files."

  - name: infrastructure_alerts
    rules:
      # Disk space alerts
      - alert: DiskSpaceLow
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "Disk space is running low"
          description: "Disk space is below 10% on {{ $labels.instance }}. Available: {{ $value | humanizePercentage }}"

      - alert: DiskSpaceCritical
        expr: (node_filesystem_avail_bytes / node_filesystem_size_bytes) < 0.05
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Disk space critically low"
          description: "Disk space is below 5% on {{ $labels.instance }}. Available: {{ $value | humanizePercentage }}"

      # Database alerts (if using PostgreSQL)
      - alert: PostgreSQLDown
        expr: pg_up == 0
        for: 1m
        labels:
          severity: critical
          service: postgresql
        annotations:
          summary: "PostgreSQL is down"
          description: "PostgreSQL database is not responding."

      - alert: PostgreSQLTooManyConnections
        expr: pg_stat_database_numbackends / pg_settings_max_connections > 0.8
        for: 5m
        labels:
          severity: warning
          service: postgresql
        annotations:
          summary: "PostgreSQL has too many connections"
          description: "PostgreSQL is using {{ $value | humanizePercentage }} of available connections."

  - name: api_alerts
    rules:
      # API connectivity alerts
      - alert: DLDAPIUnreachable
        expr: dld_api_connectivity_status != 1
        for: 2m
        labels:
          severity: critical
          service: dld-api
        annotations:
          summary: "DLD API is unreachable"
          description: "Cannot connect to Dubai Land Department API for more than 2 minutes."

      - alert: DLDAPISlowResponse
        expr: dld_api_response_time_seconds > 30
        for: 3m
        labels:
          severity: warning
          service: dld-api
        annotations:
          summary: "DLD API slow response"
          description: "DLD API response time is above 30 seconds. Current: {{ $value }}s"

  - name: business_alerts
    rules:
      # Business logic alerts
      - alert: DLDETLDataQualityIssue
        expr: dld_etl_data_quality_score < 0.95
        for: 5m
        labels:
          severity: warning
          service: dld-etl
        annotations:
          summary: "Data quality issue detected"
          description: "Data quality score is below 95%. Current score: {{ $value | humanizePercentage }}"

      - alert: DLDETLUnusualRecordCount
        expr: abs(dld_etl_daily_record_count - avg_over_time(dld_etl_daily_record_count[7d])) > (3 * stddev_over_time(dld_etl_daily_record_count[7d]))
        for: 10m
        labels:
          severity: warning
          service: dld-etl
        annotations:
          summary: "Unusual record count detected"
          description: "Daily record count ({{ $value }}) is significantly different from the 7-day average."
