# 🚀 Deployment Guide - Dubai Real Estate ETL Platform

## 📋 Table of Contents
1. [Prerequisites](#prerequisites)
2. [Local Development Setup](#local-development-setup)
3. [Google Cloud Run Deployment](#google-cloud-run-deployment)
4. [AWS ECS Deployment](#aws-ecs-deployment)
5. [Monitoring Setup](#monitoring-setup)
6. [Troubleshooting](#troubleshooting)

---

## 🔧 Prerequisites

### **System Requirements**
- Docker 20.10+
- Docker Compose 2.0+
- Python 3.9+
- 4GB RAM minimum, 8GB recommended
- 10GB free disk space

### **Cloud Platform Requirements**

#### **Google Cloud Platform**
- GCP Project with billing enabled
- Cloud Run API enabled
- Container Registry API enabled
- Cloud Storage API enabled
- Cloud Scheduler API enabled (for automation)

#### **Amazon Web Services**
- AWS Account with appropriate permissions
- ECS service enabled
- ECR repository created
- S3 bucket for data storage
- EventBridge for scheduling (optional)

### **API Access**
- Dubai Land Department API Consumer ID
- Network access to DLD APIs

---

## 💻 Local Development Setup

### **Step 1: Clone and Setup**
```bash
# Clone the repository
git clone <repository-url>
cd dld-data

# Copy environment configuration
cp .env.example .env

# Edit .env file with your configuration
nano .env
```

### **Step 2: Configure Environment**
Update `.env` file with your settings:
```bash
# API Configuration
DLD_API_CONSUMER_ID=your_consumer_id_here

# Local Storage
RAW_DATA_DIR=data/raw
PROCESSED_DATA_DIR=data/processed
LOG_LEVEL=INFO

# Processing Configuration
BATCH_SIZE=1000
PARALLEL_PROCESSING=true
MAX_RETRIES=3
```

### **Step 3: Build and Test**
```bash
# Make deployment script executable
chmod +x deploy.sh

# Build Docker image
./deploy.sh build

# Run tests
./deploy.sh test

# Deploy locally
./deploy.sh deploy local
```

### **Step 4: Verify Local Deployment**
```bash
# Check services status
docker-compose ps

# View logs
docker-compose logs dld-etl

# Access services
# - ETL Health Check: http://localhost:8080/health
# - Jupyter Lab: http://localhost:8888 (token: dld-analysis-token)
# - Grafana: http://localhost:3000 (admin/admin)
# - Prometheus: http://localhost:9090
```

### **Step 5: Run ETL Pipeline**
```bash
# Extract data for specific date range
docker-compose exec dld-etl python extract_dld_data.py \
  --from-date "08/01/2025" \
  --to-date "08/02/2025" \
  --data-type all \
  --parallel

# Run analysis
docker-compose exec dld-etl python src/analysis.py

# Generate visualizations
docker-compose exec dld-etl python src/visualizations.py
```

---

## ☁️ Google Cloud Run Deployment

### **Step 1: Setup GCP Environment**
```bash
# Install Google Cloud SDK
curl https://sdk.cloud.google.com | bash
exec -l $SHELL

# Initialize gcloud
gcloud init

# Set project
gcloud config set project YOUR_PROJECT_ID

# Enable required APIs
gcloud services enable run.googleapis.com
gcloud services enable cloudbuild.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable scheduler.googleapis.com
```

### **Step 2: Create Cloud Resources**
```bash
# Create storage bucket
gsutil mb gs://YOUR_BUCKET_NAME

# Create service account
gcloud iam service-accounts create dld-etl-service \
  --display-name="DLD ETL Service Account"

# Grant permissions
gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
  --member="serviceAccount:dld-etl-service@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/storage.admin"

# Create service account key
gcloud iam service-accounts keys create key.json \
  --iam-account=dld-etl-service@YOUR_PROJECT_ID.iam.gserviceaccount.com
```

### **Step 3: Configure Secrets**
```bash
# Create secret for API key
gcloud secrets create dld-api-consumer-id \
  --data-file=<(echo -n "YOUR_API_CONSUMER_ID")

# Create secret for service account key
gcloud secrets create google-cloud-key \
  --data-file=key.json

# Grant access to secrets
gcloud secrets add-iam-policy-binding dld-api-consumer-id \
  --member="serviceAccount:dld-etl-service@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
  --role="roles/secretmanager.secretAccessor"
```

### **Step 4: Update Configuration**
Update `.env` file for Cloud Run:
```bash
# Cloud Configuration
CLOUD_PROVIDER=gcp
GCP_PROJECT_ID=YOUR_PROJECT_ID
GCS_BUCKET=YOUR_BUCKET_NAME
CLOUD_RUN_REGION=us-central1
CLOUD_RUN_MEMORY=4Gi
CLOUD_RUN_CPU=2
```

### **Step 5: Deploy to Cloud Run**
```bash
# Deploy using deployment script
./deploy.sh deploy cloud-run

# Or manually deploy
gcloud run deploy dld-etl-pipeline \
  --image gcr.io/YOUR_PROJECT_ID/dld-etl:latest \
  --platform managed \
  --region us-central1 \
  --memory 4Gi \
  --cpu 2 \
  --timeout 3600 \
  --concurrency 1 \
  --max-instances 10 \
  --set-env-vars "CLOUD_PROVIDER=gcp,GCS_BUCKET=YOUR_BUCKET_NAME" \
  --set-secrets "DLD_API_CONSUMER_ID=dld-api-consumer-id:latest" \
  --service-account dld-etl-service@YOUR_PROJECT_ID.iam.gserviceaccount.com \
  --allow-unauthenticated
```

### **Step 6: Setup Automated Scheduling**
```bash
# Create Cloud Scheduler job for daily ETL
gcloud scheduler jobs create http daily-dld-etl \
  --schedule="0 2 * * *" \
  --uri="https://dld-etl-pipeline-YOUR_PROJECT_ID.a.run.app/trigger-etl" \
  --http-method=POST \
  --headers="Content-Type=application/json" \
  --message-body='{"from_date":"{{ ds }}","to_date":"{{ next_ds }}","data_type":"all","parallel":true}' \
  --time-zone="UTC"
```

---

## 🔶 AWS ECS Deployment

### **Step 1: Setup AWS Environment**
```bash
# Install AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# Configure AWS CLI
aws configure

# Install ECS CLI (optional)
sudo curl -Lo /usr/local/bin/ecs-cli \
  https://amazon-ecs-cli.s3.amazonaws.com/ecs-cli-linux-amd64-latest
sudo chmod +x /usr/local/bin/ecs-cli
```

### **Step 2: Create AWS Resources**
```bash
# Create ECR repository
aws ecr create-repository --repository-name dld-etl

# Create S3 bucket
aws s3 mb s3://your-dld-data-bucket

# Create ECS cluster
aws ecs create-cluster --cluster-name dld-etl-cluster

# Create task execution role
aws iam create-role \
  --role-name ecsTaskExecutionRole \
  --assume-role-policy-document file://ecs-task-execution-role.json

# Attach policy
aws iam attach-role-policy \
  --role-name ecsTaskExecutionRole \
  --policy-arn arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy
```

### **Step 3: Configure Secrets**
```bash
# Create secret in AWS Secrets Manager
aws secretsmanager create-secret \
  --name dld-api-keys \
  --description "DLD API Keys" \
  --secret-string '{"api-consumer-id":"YOUR_API_CONSUMER_ID"}'
```

### **Step 4: Update Configuration**
Update `.env` file for AWS:
```bash
# AWS Configuration
CLOUD_PROVIDER=aws
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=YOUR_ACCOUNT_ID
S3_BUCKET=your-dld-data-bucket
ECS_CLUSTER=dld-etl-cluster
ECS_SERVICE=dld-etl-service
```

### **Step 5: Deploy to ECS**
```bash
# Deploy using deployment script
./deploy.sh deploy ecs

# Or manually deploy
# 1. Build and push image to ECR
aws ecr get-login-password --region us-east-1 | \
  docker login --username AWS --password-stdin YOUR_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com

docker tag dld-etl:latest \
  YOUR_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/dld-etl:latest

docker push YOUR_ACCOUNT_ID.dkr.ecr.us-east-1.amazonaws.com/dld-etl:latest

# 2. Register task definition
aws ecs register-task-definition \
  --cli-input-json file://aws-ecs-task-definition.json

# 3. Create service
aws ecs create-service \
  --cluster dld-etl-cluster \
  --service-name dld-etl-service \
  --task-definition dld-etl-pipeline \
  --desired-count 1 \
  --launch-type FARGATE \
  --network-configuration "awsvpcConfiguration={subnets=[subnet-12345],securityGroups=[sg-12345],assignPublicIp=ENABLED}"
```

### **Step 6: Setup Automated Scheduling**
```bash
# Create EventBridge rule for daily ETL
aws events put-rule \
  --name daily-dld-etl \
  --schedule-expression "cron(0 2 * * ? *)" \
  --description "Daily DLD ETL execution"

# Add ECS task as target
aws events put-targets \
  --rule daily-dld-etl \
  --targets "Id"="1","Arn"="arn:aws:ecs:us-east-1:YOUR_ACCOUNT_ID:cluster/dld-etl-cluster","RoleArn"="arn:aws:iam::YOUR_ACCOUNT_ID:role/ecsEventsRole","EcsParameters"="{\"TaskDefinitionArn\":\"arn:aws:ecs:us-east-1:YOUR_ACCOUNT_ID:task-definition/dld-etl-pipeline\",\"LaunchType\":\"FARGATE\"}"
```

---

## 📊 Monitoring Setup

### **Step 1: Deploy Monitoring Stack**
```bash
# Start monitoring services
docker-compose up -d prometheus grafana

# Import Grafana dashboards
# Access Grafana at http://localhost:3000
# Login: admin/admin
# Import dashboard from monitoring/grafana/dashboards/
```

### **Step 2: Configure Alerts**
```bash
# Alerts are configured in monitoring/alert_rules.yml
# Update notification channels in Grafana:
# - Email notifications
# - Slack webhooks
# - PagerDuty integration
```

### **Step 3: Cloud Monitoring**

#### **Google Cloud Monitoring**
```bash
# Enable monitoring API
gcloud services enable monitoring.googleapis.com

# Create notification channels
gcloud alpha monitoring channels create \
  --display-name="Email Alerts" \
  --type=email \
  --channel-labels=email_address=<EMAIL>
```

#### **AWS CloudWatch**
```bash
# Create CloudWatch dashboard
aws cloudwatch put-dashboard \
  --dashboard-name "DLD-ETL-Dashboard" \
  --dashboard-body file://cloudwatch-dashboard.json

# Create alarms
aws cloudwatch put-metric-alarm \
  --alarm-name "DLD-ETL-High-CPU" \
  --alarm-description "DLD ETL High CPU Usage" \
  --metric-name CPUUtilization \
  --namespace AWS/ECS \
  --statistic Average \
  --period 300 \
  --threshold 80 \
  --comparison-operator GreaterThanThreshold \
  --evaluation-periods 2
```

---

## 🔧 Troubleshooting

### **Common Issues**

#### **1. Container Build Failures**
```bash
# Check Docker daemon
sudo systemctl status docker

# Clean Docker cache
docker system prune -a

# Check disk space
df -h
```

#### **2. API Connection Issues**
```bash
# Test API connectivity
curl -X POST "https://gateway.dubailand.gov.ae/open-data/rents" \
  -H "Accept: application/json" \
  -H "Content-Type: application/json" \
  -H "consumer-id: YOUR_CONSUMER_ID" \
  -d '{"P_DATE_TYPE":"0","P_FROM_DATE":"08/01/2025","P_TO_DATE":"08/02/2025","P_TAKE":"1","P_SKIP":"0"}'

# Check network connectivity
ping gateway.dubailand.gov.ae
```

#### **3. Memory Issues**
```bash
# Check memory usage
docker stats

# Increase memory limits in docker-compose.yml
# Or adjust BATCH_SIZE in .env
```

#### **4. Permission Issues**
```bash
# Fix file permissions
sudo chown -R $USER:$USER data/
chmod -R 755 data/

# Check service account permissions (Cloud)
gcloud projects get-iam-policy YOUR_PROJECT_ID
```

### **Debugging Commands**
```bash
# View container logs
docker-compose logs -f dld-etl

# Execute commands in container
docker-compose exec dld-etl bash

# Check health status
curl http://localhost:8080/health

# View detailed status
curl http://localhost:8080/status
```

### **Performance Tuning**
```bash
# Optimize for large datasets
export BATCH_SIZE=500
export MAX_WORKERS=2
export CHUNK_SIZE=5000

# Enable caching
export ENABLE_CACHING=true

# Adjust memory limits
export MEMORY_LIMIT=4096
```

---

## 📞 Support

### **Getting Help**
1. Check logs: `docker-compose logs dld-etl`
2. Review health status: `curl http://localhost:8080/health`
3. Check monitoring dashboards
4. Review troubleshooting section above

### **Reporting Issues**
Include the following information:
- Deployment platform (local/cloud-run/ecs)
- Error messages and logs
- Environment configuration
- Steps to reproduce

---

### **Quick Reference Commands**

#### **Local Development**
```bash
# Start all services
docker-compose up -d

# Run ETL for today
docker-compose exec dld-etl python extract_dld_data.py \
  --from-date "$(date +%m/%d/%Y)" \
  --to-date "$(date -d '+1 day' +%m/%d/%Y)" \
  --parallel

# Check status
curl http://localhost:8080/health | jq

# View logs
docker-compose logs -f dld-etl
```

#### **Cloud Run**
```bash
# Deploy latest version
gcloud run deploy dld-etl-pipeline \
  --image gcr.io/PROJECT_ID/dld-etl:latest \
  --region us-central1

# Trigger ETL manually
curl -X POST "https://dld-etl-pipeline-PROJECT_ID.a.run.app/trigger-etl" \
  -H "Content-Type: application/json" \
  -d '{"from_date":"08/01/2025","to_date":"08/02/2025","data_type":"all"}'

# View logs
gcloud logging read "resource.type=cloud_run_revision AND resource.labels.service_name=dld-etl-pipeline" --limit 50
```

#### **AWS ECS**
```bash
# Update service
aws ecs update-service \
  --cluster dld-etl-cluster \
  --service dld-etl-service \
  --force-new-deployment

# View logs
aws logs tail /ecs/dld-etl-pipeline --follow
```

*This deployment guide provides comprehensive instructions for deploying the Dubai Real Estate ETL Platform across different environments.*
