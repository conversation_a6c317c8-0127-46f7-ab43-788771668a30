# Dubai Real Estate ETL Platform - Environment Configuration
# Copy this file to .env and update with your actual values

# =============================================================================
# API CONFIGURATION
# =============================================================================

# Dubai Land Department API Consumer ID
DLD_API_CONSUMER_ID=gkb3WvEG0rY9eilwXC0P2pTz8UzvLj9F

# API Rate Limiting
API_RATE_LIMIT=100
API_TIMEOUT=30

# =============================================================================
# DATA STORAGE CONFIGURATION
# =============================================================================

# Local Storage Paths
RAW_DATA_DIR=data/raw
PROCESSED_DATA_DIR=data/processed
LOG_DIR=logs

# Cloud Storage Configuration (optional)
CLOUD_PROVIDER=local  # Options: local, gcp, aws, azure
CLOUD_STORAGE_BUCKET=
CLOUD_STORAGE_PREFIX=dld-data

# Google Cloud Platform
GCP_PROJECT_ID=
GCP_CREDENTIALS_PATH=
GCS_BUCKET=

# Amazon Web Services
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_REGION=us-east-1
S3_BUCKET=

# Azure
AZURE_STORAGE_ACCOUNT=
AZURE_STORAGE_KEY=
AZURE_CONTAINER=

# =============================================================================
# PROCESSING CONFIGURATION
# =============================================================================

# ETL Pipeline Settings
BATCH_SIZE=1000
PARALLEL_PROCESSING=true
MAX_WORKERS=4
MAX_RETRIES=3
RETRY_DELAY=5

# Data Processing
CHUNK_SIZE=10000
MEMORY_LIMIT=2048  # MB
ENABLE_CACHING=true

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log Level: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s
LOG_ROTATION=daily
LOG_RETENTION_DAYS=30

# =============================================================================
# DATABASE CONFIGURATION (Optional)
# =============================================================================

# PostgreSQL Database
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=dld_warehouse
POSTGRES_USER=dld_user
POSTGRES_PASSWORD=dld_password

# Redis Cache
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================

# Prometheus Metrics
ENABLE_METRICS=true
METRICS_PORT=8080
METRICS_PATH=/metrics

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_PORT=8080
HEALTH_CHECK_PATH=/health

# Alerting
ALERT_EMAIL=
SLACK_WEBHOOK_URL=
ALERT_THRESHOLD_ERROR_RATE=0.05

# =============================================================================
# JUPYTER NOTEBOOK CONFIGURATION
# =============================================================================

# Jupyter Lab Settings
JUPYTER_TOKEN=dld-analysis-token
JUPYTER_PORT=8888
JUPYTER_ENABLE_LAB=yes

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# API Security
API_KEY_ENCRYPTION=true
ENABLE_SSL=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# Container Security
RUN_AS_NON_ROOT=true
SECURITY_CONTEXT_USER=1000

# =============================================================================
# PERFORMANCE TUNING
# =============================================================================

# Memory Settings
JAVA_OPTS=-Xmx2g
PYTHON_MEMORY_LIMIT=2048

# CPU Settings
CPU_LIMIT=2
CPU_REQUEST=1

# Network Settings
CONNECTION_POOL_SIZE=20
CONNECTION_TIMEOUT=30
READ_TIMEOUT=60

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development Mode
DEBUG_MODE=false
DEVELOPMENT_MODE=false
ENABLE_PROFILING=false

# Testing
TEST_DATA_SIZE=1000
MOCK_API_RESPONSES=false
SKIP_EXTERNAL_APIS=false

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Container Settings
CONTAINER_REGISTRY=gcr.io
IMAGE_TAG=latest
DEPLOYMENT_ENVIRONMENT=production

# Kubernetes
NAMESPACE=dld-etl
REPLICA_COUNT=1
RESOURCE_REQUESTS_CPU=500m
RESOURCE_REQUESTS_MEMORY=1Gi
RESOURCE_LIMITS_CPU=2
RESOURCE_LIMITS_MEMORY=4Gi

# Cloud Run
CLOUD_RUN_REGION=us-central1
CLOUD_RUN_MEMORY=2Gi
CLOUD_RUN_CPU=2
CLOUD_RUN_CONCURRENCY=10
CLOUD_RUN_TIMEOUT=3600

# AWS ECS
ECS_CLUSTER=dld-etl-cluster
ECS_SERVICE=dld-etl-service
ECS_TASK_DEFINITION=dld-etl-task
ECS_CPU=1024
ECS_MEMORY=2048

# =============================================================================
# SCHEDULING CONFIGURATION
# =============================================================================

# Cron Schedule for Daily ETL
ETL_SCHEDULE=0 2 * * *  # Daily at 2 AM UTC
ENABLE_SCHEDULER=true
SCHEDULER_TIMEZONE=UTC

# Airflow Configuration
AIRFLOW_WEBSERVER_PORT=8080
AIRFLOW_EXECUTOR=LocalExecutor
AIRFLOW_LOAD_EXAMPLES=false

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature Toggles
ENABLE_PARALLEL_PROCESSING=true
ENABLE_DATA_VALIDATION=true
ENABLE_ANOMALY_DETECTION=false
ENABLE_ML_PREDICTIONS=false
ENABLE_REAL_TIME_PROCESSING=false

# Experimental Features
ENABLE_STREAMING_ETL=false
ENABLE_AUTO_SCALING=false
ENABLE_PREDICTIVE_CACHING=false
