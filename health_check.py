#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Health Check and Monitoring Endpoints for Dubai Real Estate ETL Platform
Provides health checks, metrics, and status endpoints for monitoring.
"""

import os
import json
import time
import psutil
import logging
from datetime import datetime, timedelta
from flask import Flask, jsonify, request
from typing import Dict, Any, Optional
import requests

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("DLD_Health_Check")

app = Flask(__name__)

class HealthChecker:
    """Health check and monitoring utilities for the ETL platform."""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.last_etl_run = None
        self.etl_status = "unknown"
        self.metrics = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "last_error": None
        }
    
    def get_system_health(self) -> Dict[str, Any]:
        """Get system health metrics."""
        try:
            # CPU and Memory usage
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Network connectivity test
            network_status = self._check_network_connectivity()
            
            # API connectivity test
            api_status = self._check_api_connectivity()
            
            return {
                "status": "healthy" if cpu_percent < 90 and memory.percent < 90 else "degraded",
                "timestamp": datetime.now().isoformat(),
                "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
                "system": {
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory.percent,
                    "memory_available_gb": memory.available / (1024**3),
                    "disk_percent": disk.percent,
                    "disk_free_gb": disk.free / (1024**3)
                },
                "connectivity": {
                    "network": network_status,
                    "dld_api": api_status
                },
                "etl": {
                    "last_run": self.last_etl_run.isoformat() if self.last_etl_run else None,
                    "status": self.etl_status
                }
            }
        except Exception as e:
            logger.error(f"Error getting system health: {e}")
            return {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def _check_network_connectivity(self) -> str:
        """Check basic network connectivity."""
        try:
            response = requests.get("https://www.google.com", timeout=5)
            return "healthy" if response.status_code == 200 else "degraded"
        except Exception:
            return "unhealthy"
    
    def _check_api_connectivity(self) -> str:
        """Check DLD API connectivity."""
        try:
            # Test DLD API endpoint
            url = "https://gateway.dubailand.gov.ae/open-data/rents"
            headers = {
                'Accept': 'application/json, */*',
                'Content-Type': 'application/json; charset=UTF-8',
                'consumer-id': os.getenv('DLD_API_CONSUMER_ID', 'gkb3WvEG0rY9eilwXC0P2pTz8UzvLj9F')
            }
            
            # Simple test payload
            payload = {
                "P_DATE_TYPE": "0",
                "P_FROM_DATE": "08/01/2025",
                "P_TO_DATE": "08/02/2025",
                "P_TAKE": "1",
                "P_SKIP": "0"
            }
            
            response = requests.post(url, headers=headers, json=payload, timeout=10)
            return "healthy" if response.status_code == 200 else "degraded"
        except Exception as e:
            logger.warning(f"API connectivity check failed: {e}")
            return "unhealthy"
    
    def get_data_status(self) -> Dict[str, Any]:
        """Get data freshness and quality status."""
        try:
            data_dir = os.getenv('PROCESSED_DATA_DIR', 'data/processed')
            
            # Check for recent data files
            recent_files = []
            if os.path.exists(data_dir):
                for file in os.listdir(data_dir):
                    if file.endswith('.csv'):
                        file_path = os.path.join(data_dir, file)
                        file_stat = os.stat(file_path)
                        file_age = datetime.now() - datetime.fromtimestamp(file_stat.st_mtime)
                        
                        recent_files.append({
                            "filename": file,
                            "size_mb": file_stat.st_size / (1024**2),
                            "age_hours": file_age.total_seconds() / 3600,
                            "last_modified": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                        })
            
            # Sort by most recent
            recent_files.sort(key=lambda x: x['age_hours'])
            
            # Determine data freshness status
            if recent_files:
                newest_file_age = recent_files[0]['age_hours']
                if newest_file_age < 24:
                    data_status = "fresh"
                elif newest_file_age < 48:
                    data_status = "stale"
                else:
                    data_status = "outdated"
            else:
                data_status = "no_data"
            
            return {
                "status": data_status,
                "timestamp": datetime.now().isoformat(),
                "files_count": len(recent_files),
                "recent_files": recent_files[:5],  # Show only 5 most recent
                "total_size_mb": sum(f['size_mb'] for f in recent_files)
            }
        except Exception as e:
            logger.error(f"Error getting data status: {e}")
            return {
                "status": "error",
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def update_etl_status(self, status: str, timestamp: Optional[datetime] = None):
        """Update ETL status."""
        self.etl_status = status
        self.last_etl_run = timestamp or datetime.now()
    
    def record_request(self, success: bool, error: Optional[str] = None):
        """Record request metrics."""
        self.metrics["total_requests"] += 1
        if success:
            self.metrics["successful_requests"] += 1
        else:
            self.metrics["failed_requests"] += 1
            self.metrics["last_error"] = error

# Global health checker instance
health_checker = HealthChecker()

@app.route('/health', methods=['GET'])
def health_check():
    """Basic health check endpoint."""
    try:
        health_data = health_checker.get_system_health()
        status_code = 200 if health_data["status"] in ["healthy", "degraded"] else 503
        
        health_checker.record_request(True)
        return jsonify(health_data), status_code
    except Exception as e:
        health_checker.record_request(False, str(e))
        return jsonify({
            "status": "error",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 503

@app.route('/ready', methods=['GET'])
def readiness_check():
    """Readiness check endpoint."""
    try:
        # Check if the application is ready to serve requests
        health_data = health_checker.get_system_health()
        data_status = health_checker.get_data_status()
        
        # Application is ready if system is healthy and API is accessible
        is_ready = (
            health_data["status"] in ["healthy", "degraded"] and
            health_data["connectivity"]["dld_api"] in ["healthy", "degraded"]
        )
        
        response = {
            "ready": is_ready,
            "timestamp": datetime.now().isoformat(),
            "system_status": health_data["status"],
            "api_status": health_data["connectivity"]["dld_api"],
            "data_status": data_status["status"]
        }
        
        status_code = 200 if is_ready else 503
        health_checker.record_request(True)
        return jsonify(response), status_code
    except Exception as e:
        health_checker.record_request(False, str(e))
        return jsonify({
            "ready": False,
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 503

@app.route('/metrics', methods=['GET'])
def metrics():
    """Prometheus-style metrics endpoint."""
    try:
        health_data = health_checker.get_system_health()
        data_status = health_checker.get_data_status()
        
        # Generate Prometheus-style metrics
        metrics_text = f"""# HELP dld_etl_up Whether the ETL service is up
# TYPE dld_etl_up gauge
dld_etl_up{{status="{health_data['status']}"}} {1 if health_data['status'] != 'error' else 0}

# HELP dld_etl_uptime_seconds Total uptime in seconds
# TYPE dld_etl_uptime_seconds counter
dld_etl_uptime_seconds {health_data['uptime_seconds']}

# HELP dld_etl_cpu_percent CPU usage percentage
# TYPE dld_etl_cpu_percent gauge
dld_etl_cpu_percent {health_data['system']['cpu_percent']}

# HELP dld_etl_memory_percent Memory usage percentage
# TYPE dld_etl_memory_percent gauge
dld_etl_memory_percent {health_data['system']['memory_percent']}

# HELP dld_etl_requests_total Total number of requests
# TYPE dld_etl_requests_total counter
dld_etl_requests_total {health_checker.metrics['total_requests']}

# HELP dld_etl_requests_successful_total Total number of successful requests
# TYPE dld_etl_requests_successful_total counter
dld_etl_requests_successful_total {health_checker.metrics['successful_requests']}

# HELP dld_etl_requests_failed_total Total number of failed requests
# TYPE dld_etl_requests_failed_total counter
dld_etl_requests_failed_total {health_checker.metrics['failed_requests']}

# HELP dld_etl_data_files_count Number of data files
# TYPE dld_etl_data_files_count gauge
dld_etl_data_files_count {data_status['files_count']}

# HELP dld_etl_data_size_mb Total data size in MB
# TYPE dld_etl_data_size_mb gauge
dld_etl_data_size_mb {data_status['total_size_mb']}
"""
        
        health_checker.record_request(True)
        return metrics_text, 200, {'Content-Type': 'text/plain; charset=utf-8'}
    except Exception as e:
        health_checker.record_request(False, str(e))
        return f"# Error generating metrics: {str(e)}", 503, {'Content-Type': 'text/plain; charset=utf-8'}

@app.route('/status', methods=['GET'])
def detailed_status():
    """Detailed status endpoint with comprehensive information."""
    try:
        health_data = health_checker.get_system_health()
        data_status = health_checker.get_data_status()
        
        response = {
            "service": "Dubai Real Estate ETL Platform",
            "version": "1.0.0",
            "timestamp": datetime.now().isoformat(),
            "health": health_data,
            "data": data_status,
            "metrics": health_checker.metrics,
            "environment": {
                "python_version": os.sys.version,
                "platform": os.name,
                "working_directory": os.getcwd(),
                "environment_variables": {
                    "LOG_LEVEL": os.getenv('LOG_LEVEL', 'INFO'),
                    "CLOUD_PROVIDER": os.getenv('CLOUD_PROVIDER', 'local'),
                    "BATCH_SIZE": os.getenv('BATCH_SIZE', '1000'),
                    "PARALLEL_PROCESSING": os.getenv('PARALLEL_PROCESSING', 'true')
                }
            }
        }
        
        health_checker.record_request(True)
        return jsonify(response), 200
    except Exception as e:
        health_checker.record_request(False, str(e))
        return jsonify({
            "service": "Dubai Real Estate ETL Platform",
            "timestamp": datetime.now().isoformat(),
            "error": str(e)
        }), 503

@app.route('/trigger-etl', methods=['POST'])
def trigger_etl():
    """Endpoint to trigger ETL pipeline execution."""
    try:
        # Get parameters from request
        data = request.get_json() or {}
        from_date = data.get('from_date')
        to_date = data.get('to_date')
        data_type = data.get('data_type', 'all')
        parallel = data.get('parallel', True)
        
        if not from_date or not to_date:
            return jsonify({
                "error": "from_date and to_date are required",
                "timestamp": datetime.now().isoformat()
            }), 400
        
        # Update ETL status
        health_checker.update_etl_status("running")
        
        # Here you would trigger the actual ETL process
        # For now, we'll simulate it
        logger.info(f"ETL triggered: {from_date} to {to_date}, type: {data_type}, parallel: {parallel}")
        
        # Simulate ETL completion
        health_checker.update_etl_status("completed")
        
        return jsonify({
            "message": "ETL pipeline triggered successfully",
            "parameters": {
                "from_date": from_date,
                "to_date": to_date,
                "data_type": data_type,
                "parallel": parallel
            },
            "timestamp": datetime.now().isoformat()
        }), 200
    except Exception as e:
        health_checker.update_etl_status("failed")
        health_checker.record_request(False, str(e))
        return jsonify({
            "error": str(e),
            "timestamp": datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    port = int(os.getenv('HEALTH_CHECK_PORT', 8080))
    debug = os.getenv('DEBUG_MODE', 'false').lower() == 'true'
    
    logger.info(f"Starting health check service on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
