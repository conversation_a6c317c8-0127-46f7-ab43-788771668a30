# 🔧 Operational Procedures - Dubai Real Estate ETL Platform

## 📋 Table of Contents
1. [Daily Operations](#daily-operations)
2. [Weekly Maintenance](#weekly-maintenance)
3. [Monthly Reviews](#monthly-reviews)
4. [Incident Response](#incident-response)
5. [Performance Monitoring](#performance-monitoring)
6. [Data Quality Assurance](#data-quality-assurance)
7. [Backup and Recovery](#backup-and-recovery)

---

## 📅 Daily Operations

### **Morning Health Check (9:00 AM UTC)**

#### **1. System Status Verification**
```bash
# Check overall system health
curl -s http://your-etl-endpoint/health | jq

# Expected response:
{
  "status": "healthy",
  "timestamp": "2025-08-10T09:00:00Z",
  "uptime_seconds": 86400,
  "system": {
    "cpu_percent": 25.5,
    "memory_percent": 45.2,
    "disk_percent": 30.1
  },
  "connectivity": {
    "network": "healthy",
    "dld_api": "healthy"
  }
}
```

#### **2. ETL Pipeline Status**
```bash
# Check last ETL run status
curl -s http://your-etl-endpoint/status | jq '.etl'

# Verify data freshness
ls -la data/processed/ | head -10

# Check for any failed runs
grep -i "error\|failed" logs/dld_*_extraction.log | tail -20
```

#### **3. Data Quality Checks**
```bash
# Verify record counts are within expected range
python -c "
import pandas as pd
import glob

# Check latest files
files = glob.glob('data/processed/*_2025-*.csv')
for file in sorted(files)[-2:]:
    df = pd.read_csv(file)
    print(f'{file}: {len(df)} records')
"

# Check for data anomalies
python scripts/data_quality_check.py --date $(date +%Y-%m-%d)
```

### **ETL Execution Monitoring**

#### **Automated Daily ETL (2:00 AM UTC)**
The ETL pipeline runs automatically via:
- **Cloud Run**: Cloud Scheduler triggers
- **AWS ECS**: EventBridge rules
- **Local**: Cron job

#### **Manual ETL Trigger (if needed)**
```bash
# For current date
curl -X POST http://your-etl-endpoint/trigger-etl \
  -H "Content-Type: application/json" \
  -d '{
    "from_date": "'$(date +%m/%d/%Y)'",
    "to_date": "'$(date -d '+1 day' +%m/%d/%Y)'",
    "data_type": "all",
    "parallel": true
  }'

# For specific date range
curl -X POST http://your-etl-endpoint/trigger-etl \
  -H "Content-Type: application/json" \
  -d '{
    "from_date": "08/01/2025",
    "to_date": "08/02/2025",
    "data_type": "all",
    "parallel": true
  }'
```

### **Alert Response Procedures**

#### **High Priority Alerts (Immediate Response)**
1. **Service Down**: Check container status, restart if needed
2. **API Unreachable**: Verify network connectivity, check DLD API status
3. **Data Pipeline Failure**: Review logs, identify root cause, restart pipeline

#### **Medium Priority Alerts (Within 2 hours)**
1. **High Resource Usage**: Monitor trends, scale if necessary
2. **Data Quality Issues**: Investigate data anomalies, validate sources
3. **Slow Performance**: Check system resources, optimize if needed

---

## 🔄 Weekly Maintenance

### **Every Monday (10:00 AM UTC)**

#### **1. System Performance Review**
```bash
# Generate weekly performance report
python scripts/weekly_performance_report.py \
  --start-date $(date -d '7 days ago' +%Y-%m-%d) \
  --end-date $(date +%Y-%m-%d)

# Check resource utilization trends
# - CPU usage patterns
# - Memory consumption
# - Disk space growth
# - Network I/O patterns
```

#### **2. Log Management**
```bash
# Archive old logs (keep last 30 days)
find logs/ -name "*.log" -mtime +30 -exec gzip {} \;
find logs/ -name "*.log.gz" -mtime +90 -delete

# Rotate current logs
logrotate /etc/logrotate.d/dld-etl

# Clean up temporary files
find /tmp -name "dld_*" -mtime +7 -delete
```

#### **3. Data Storage Cleanup**
```bash
# Archive old raw data (keep last 60 days)
find data/raw/ -name "*.json" -mtime +60 -exec gzip {} \;
find data/raw/ -name "*.json.gz" -mtime +180 -delete

# Verify processed data integrity
python scripts/data_integrity_check.py --days 7
```

#### **4. Security Updates**
```bash
# Update container images
docker pull python:3.9-slim
./deploy.sh build

# Check for security vulnerabilities
docker scan dld-etl:latest

# Update dependencies
pip-audit --requirements requirements.txt
```

### **Every Friday (3:00 PM UTC)**

#### **1. Backup Verification**
```bash
# Verify backup completeness
python scripts/backup_verification.py

# Test restore procedure (on staging)
python scripts/test_restore.py --backup-date $(date -d '1 day ago' +%Y-%m-%d)
```

#### **2. Performance Optimization**
```bash
# Analyze query performance
python scripts/performance_analysis.py --days 7

# Optimize data storage
python scripts/optimize_storage.py

# Update configuration if needed
```

---

## 📊 Monthly Reviews

### **First Monday of Each Month**

#### **1. Capacity Planning Review**
- Analyze storage growth trends
- Review compute resource utilization
- Plan for scaling requirements
- Update cost projections

#### **2. Data Quality Assessment**
```bash
# Generate monthly data quality report
python scripts/monthly_data_quality_report.py \
  --month $(date +%Y-%m)

# Review data completeness metrics
# Check for systematic data issues
# Update validation rules if needed
```

#### **3. Performance Benchmarking**
```bash
# Run performance benchmarks
python scripts/performance_benchmark.py

# Compare with previous months
# Identify performance degradation
# Plan optimization initiatives
```

#### **4. Security Review**
- Review access logs
- Update API keys and credentials
- Check for unauthorized access attempts
- Update security policies

---

## 🚨 Incident Response

### **Incident Classification**

#### **Severity 1 (Critical) - Response Time: 15 minutes**
- Complete service outage
- Data corruption or loss
- Security breach
- API completely unavailable

#### **Severity 2 (High) - Response Time: 1 hour**
- Partial service degradation
- ETL pipeline failures
- Data quality issues affecting >50% of data
- Performance degradation >50%

#### **Severity 3 (Medium) - Response Time: 4 hours**
- Minor performance issues
- Data quality issues affecting <50% of data
- Non-critical feature failures

#### **Severity 4 (Low) - Response Time: Next business day**
- Documentation issues
- Minor UI/UX problems
- Enhancement requests

### **Incident Response Procedures**

#### **1. Initial Response (First 15 minutes)**
```bash
# Assess the situation
curl -s http://your-etl-endpoint/health
curl -s http://your-etl-endpoint/status

# Check system resources
docker stats
df -h

# Review recent logs
tail -100 logs/dld_*_extraction.log
```

#### **2. Escalation Matrix**
- **Severity 1**: Notify on-call engineer immediately
- **Severity 2**: Notify team lead within 1 hour
- **Severity 3**: Create ticket, assign to next available engineer
- **Severity 4**: Add to backlog for next sprint

#### **3. Communication Templates**

**Initial Notification:**
```
INCIDENT: [Severity] - [Brief Description]
Time: [UTC Timestamp]
Impact: [Description of user impact]
Status: Investigating
ETA: [Estimated resolution time]
```

**Status Update:**
```
INCIDENT UPDATE: [Incident ID]
Status: [In Progress/Resolved/Escalated]
Actions Taken: [Brief description]
Next Steps: [What's being done next]
ETA: [Updated estimate]
```

**Resolution Notice:**
```
INCIDENT RESOLVED: [Incident ID]
Resolution Time: [UTC Timestamp]
Root Cause: [Brief explanation]
Actions Taken: [What was done to resolve]
Prevention: [Steps to prevent recurrence]
```

---

## 📈 Performance Monitoring

### **Key Performance Indicators (KPIs)**

#### **System Performance**
- **CPU Utilization**: Target <70% average
- **Memory Usage**: Target <80% average
- **Disk I/O**: Monitor for bottlenecks
- **Network Latency**: Target <100ms to DLD API

#### **ETL Performance**
- **Processing Time**: Target <30 minutes per day
- **Success Rate**: Target >99%
- **Data Freshness**: Target <24 hours
- **Error Rate**: Target <1%

#### **Data Quality**
- **Completeness**: Target >95%
- **Accuracy**: Target >99%
- **Consistency**: Target >98%
- **Timeliness**: Target <24 hours lag

### **Monitoring Tools and Dashboards**

#### **Grafana Dashboards**
1. **System Overview**: CPU, memory, disk, network
2. **ETL Pipeline**: Success rates, processing times, error counts
3. **Data Quality**: Completeness, accuracy, freshness metrics
4. **Business Metrics**: Record counts, data volumes, trends

#### **Alert Thresholds**
```yaml
# System Alerts
cpu_usage_warning: 70%
cpu_usage_critical: 85%
memory_usage_warning: 75%
memory_usage_critical: 90%
disk_usage_warning: 80%
disk_usage_critical: 95%

# ETL Alerts
processing_time_warning: 45 minutes
processing_time_critical: 60 minutes
error_rate_warning: 2%
error_rate_critical: 5%
data_freshness_warning: 36 hours
data_freshness_critical: 48 hours
```

---

## ✅ Data Quality Assurance

### **Daily Data Quality Checks**

#### **1. Completeness Validation**
```python
# Check for missing required fields
def validate_completeness(df, required_fields):
    missing_data = {}
    for field in required_fields:
        missing_count = df[field].isnull().sum()
        missing_pct = (missing_count / len(df)) * 100
        if missing_pct > 5:  # Alert if >5% missing
            missing_data[field] = missing_pct
    return missing_data
```

#### **2. Accuracy Validation**
```python
# Check for data anomalies
def validate_accuracy(df):
    anomalies = []
    
    # Price per sqm should be reasonable
    invalid_prices = df[
        (df['PRICE_PER_SQM'] < 100) | 
        (df['PRICE_PER_SQM'] > 50000)
    ]
    if len(invalid_prices) > 0:
        anomalies.append(f"Invalid prices: {len(invalid_prices)} records")
    
    # Area should be reasonable
    invalid_areas = df[
        (df['ACTUAL_AREA'] < 10) | 
        (df['ACTUAL_AREA'] > 10000)
    ]
    if len(invalid_areas) > 0:
        anomalies.append(f"Invalid areas: {len(invalid_areas)} records")
    
    return anomalies
```

#### **3. Consistency Validation**
```python
# Check for data consistency
def validate_consistency(df):
    issues = []
    
    # Check date consistency
    invalid_dates = df[df['REGISTRATION_DATE'] > pd.Timestamp.now()]
    if len(invalid_dates) > 0:
        issues.append(f"Future dates: {len(invalid_dates)} records")
    
    # Check value consistency
    invalid_values = df[df['TRANS_VALUE'] <= 0]
    if len(invalid_values) > 0:
        issues.append(f"Invalid transaction values: {len(invalid_values)} records")
    
    return issues
```

---

## 💾 Backup and Recovery

### **Backup Strategy**

#### **Data Backup Schedule**
- **Raw Data**: Daily incremental, weekly full backup
- **Processed Data**: Daily incremental, weekly full backup
- **Configuration**: Daily backup
- **Logs**: Weekly backup, monthly archive

#### **Backup Locations**
- **Primary**: Cloud storage (GCS/S3)
- **Secondary**: Different region/availability zone
- **Tertiary**: Local backup for critical data

### **Backup Procedures**

#### **Daily Backup Script**
```bash
#!/bin/bash
# daily_backup.sh

DATE=$(date +%Y-%m-%d)
BACKUP_DIR="/backups/$DATE"

# Create backup directory
mkdir -p $BACKUP_DIR

# Backup processed data
tar -czf $BACKUP_DIR/processed_data_$DATE.tar.gz data/processed/

# Backup configuration
cp -r config/ $BACKUP_DIR/

# Upload to cloud storage
if [ "$CLOUD_PROVIDER" = "gcp" ]; then
    gsutil -m cp -r $BACKUP_DIR gs://$BACKUP_BUCKET/daily/
elif [ "$CLOUD_PROVIDER" = "aws" ]; then
    aws s3 sync $BACKUP_DIR s3://$BACKUP_BUCKET/daily/$DATE/
fi

# Clean up local backups older than 7 days
find /backups -type d -mtime +7 -exec rm -rf {} \;
```

### **Recovery Procedures**

#### **Data Recovery**
```bash
# Restore from specific date
./scripts/restore_data.sh --date 2025-08-09 --type processed

# Restore configuration
./scripts/restore_config.sh --date 2025-08-09

# Verify restoration
python scripts/verify_restoration.py --date 2025-08-09
```

#### **Disaster Recovery**
1. **Assessment**: Determine scope of data loss
2. **Recovery**: Restore from most recent backup
3. **Validation**: Verify data integrity
4. **Restart**: Resume ETL operations
5. **Post-mortem**: Document lessons learned

---

## 📞 Contact Information

### **On-Call Rotation**
- **Primary**: Data Engineering Team Lead
- **Secondary**: Senior Data Engineer
- **Escalation**: Engineering Manager

### **Emergency Contacts**
- **Data Engineering Team**: <EMAIL>
- **Infrastructure Team**: <EMAIL>
- **Security Team**: <EMAIL>

### **External Contacts**
- **DLD API Support**: [Contact information if available]
- **Cloud Provider Support**: [Support channels]

---

*These operational procedures ensure reliable, efficient, and secure operation of the Dubai Real Estate ETL Platform.*
