#!/bin/bash

# Dubai Real Estate ETL Platform - Deployment Script
# This script handles deployment to various cloud platforms

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="dld-etl"
IMAGE_TAG=${IMAGE_TAG:-latest}
ENVIRONMENT=${ENVIRONMENT:-production}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    # Check if .env file exists
    if [ ! -f .env ]; then
        log_warning ".env file not found. Creating from .env.example..."
        cp .env.example .env
        log_warning "Please update .env file with your actual configuration before proceeding."
        exit 1
    fi
    
    log_success "Prerequisites check completed"
}

# Build Docker image
build_image() {
    log_info "Building Docker image..."
    
    docker build -t ${PROJECT_NAME}:${IMAGE_TAG} .
    
    if [ $? -eq 0 ]; then
        log_success "Docker image built successfully: ${PROJECT_NAME}:${IMAGE_TAG}"
    else
        log_error "Failed to build Docker image"
        exit 1
    fi
}

# Test the application
test_application() {
    log_info "Running application tests..."
    
    # Run unit tests
    docker run --rm \
        -v $(pwd)/tests:/app/tests \
        ${PROJECT_NAME}:${IMAGE_TAG} \
        python -m pytest tests/ -v
    
    if [ $? -eq 0 ]; then
        log_success "All tests passed"
    else
        log_error "Tests failed"
        exit 1
    fi
}

# Deploy to Google Cloud Run
deploy_cloud_run() {
    log_info "Deploying to Google Cloud Run..."
    
    # Check if gcloud is installed
    if ! command -v gcloud &> /dev/null; then
        log_error "gcloud CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Load environment variables
    source .env
    
    # Set project
    gcloud config set project ${GCP_PROJECT_ID}
    
    # Build and push image to Container Registry
    log_info "Building and pushing image to GCR..."
    gcloud builds submit --tag gcr.io/${GCP_PROJECT_ID}/${PROJECT_NAME}:${IMAGE_TAG}
    
    # Deploy to Cloud Run
    log_info "Deploying to Cloud Run..."
    gcloud run deploy ${PROJECT_NAME} \
        --image gcr.io/${GCP_PROJECT_ID}/${PROJECT_NAME}:${IMAGE_TAG} \
        --platform managed \
        --region ${CLOUD_RUN_REGION:-us-central1} \
        --memory ${CLOUD_RUN_MEMORY:-4Gi} \
        --cpu ${CLOUD_RUN_CPU:-2} \
        --timeout ${CLOUD_RUN_TIMEOUT:-3600} \
        --concurrency ${CLOUD_RUN_CONCURRENCY:-1} \
        --max-instances ${CLOUD_RUN_MAX_INSTANCES:-10} \
        --set-env-vars "CLOUD_PROVIDER=gcp,GCS_BUCKET=${GCS_BUCKET},LOG_LEVEL=${LOG_LEVEL}" \
        --set-secrets "DLD_API_CONSUMER_ID=dld-api-consumer-id:latest" \
        --allow-unauthenticated
    
    if [ $? -eq 0 ]; then
        log_success "Successfully deployed to Google Cloud Run"
        
        # Get the service URL
        SERVICE_URL=$(gcloud run services describe ${PROJECT_NAME} \
            --platform managed \
            --region ${CLOUD_RUN_REGION:-us-central1} \
            --format 'value(status.url)')
        
        log_success "Service URL: ${SERVICE_URL}"
    else
        log_error "Failed to deploy to Google Cloud Run"
        exit 1
    fi
}

# Deploy to AWS ECS
deploy_aws_ecs() {
    log_info "Deploying to AWS ECS..."
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first."
        exit 1
    fi
    
    # Load environment variables
    source .env
    
    # Build and push image to ECR
    log_info "Building and pushing image to ECR..."
    
    # Get ECR login token
    aws ecr get-login-password --region ${AWS_REGION} | \
        docker login --username AWS --password-stdin ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com
    
    # Tag and push image
    docker tag ${PROJECT_NAME}:${IMAGE_TAG} \
        ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}:${IMAGE_TAG}
    
    docker push ${AWS_ACCOUNT_ID}.dkr.ecr.${AWS_REGION}.amazonaws.com/${PROJECT_NAME}:${IMAGE_TAG}
    
    # Update task definition
    log_info "Updating ECS task definition..."
    
    # Replace placeholders in task definition
    sed -e "s/ACCOUNT_ID/${AWS_ACCOUNT_ID}/g" \
        -e "s/REGION/${AWS_REGION}/g" \
        aws-ecs-task-definition.json > aws-ecs-task-definition-updated.json
    
    # Register new task definition
    aws ecs register-task-definition \
        --cli-input-json file://aws-ecs-task-definition-updated.json
    
    # Update service
    log_info "Updating ECS service..."
    aws ecs update-service \
        --cluster ${ECS_CLUSTER} \
        --service ${ECS_SERVICE} \
        --task-definition ${PROJECT_NAME}
    
    if [ $? -eq 0 ]; then
        log_success "Successfully deployed to AWS ECS"
    else
        log_error "Failed to deploy to AWS ECS"
        exit 1
    fi
    
    # Clean up
    rm -f aws-ecs-task-definition-updated.json
}

# Deploy locally with Docker Compose
deploy_local() {
    log_info "Deploying locally with Docker Compose..."
    
    # Check if docker-compose is installed
    if ! command -v docker-compose &> /dev/null; then
        log_error "docker-compose is not installed. Please install it first."
        exit 1
    fi
    
    # Build and start services
    docker-compose up -d --build
    
    if [ $? -eq 0 ]; then
        log_success "Successfully deployed locally"
        log_info "Services available at:"
        log_info "  - ETL Pipeline: http://localhost:8080"
        log_info "  - Jupyter Lab: http://localhost:8888"
        log_info "  - Grafana: http://localhost:3000"
        log_info "  - Prometheus: http://localhost:9090"
    else
        log_error "Failed to deploy locally"
        exit 1
    fi
}

# Main deployment function
deploy() {
    local platform=$1
    
    case $platform in
        "local")
            deploy_local
            ;;
        "cloud-run"|"gcp")
            deploy_cloud_run
            ;;
        "ecs"|"aws")
            deploy_aws_ecs
            ;;
        *)
            log_error "Unknown platform: $platform"
            log_info "Supported platforms: local, cloud-run, ecs"
            exit 1
            ;;
    esac
}

# Show usage
show_usage() {
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  build                 Build Docker image"
    echo "  test                  Run tests"
    echo "  deploy [platform]     Deploy to specified platform"
    echo "  check                 Check prerequisites"
    echo ""
    echo "Platforms:"
    echo "  local                 Deploy locally with Docker Compose"
    echo "  cloud-run, gcp        Deploy to Google Cloud Run"
    echo "  ecs, aws              Deploy to AWS ECS"
    echo ""
    echo "Environment Variables:"
    echo "  IMAGE_TAG             Docker image tag (default: latest)"
    echo "  ENVIRONMENT           Deployment environment (default: production)"
    echo ""
    echo "Examples:"
    echo "  $0 build"
    echo "  $0 test"
    echo "  $0 deploy local"
    echo "  $0 deploy cloud-run"
    echo "  IMAGE_TAG=v1.0.0 $0 deploy ecs"
}

# Main script logic
main() {
    case $1 in
        "build")
            check_prerequisites
            build_image
            ;;
        "test")
            check_prerequisites
            build_image
            test_application
            ;;
        "deploy")
            if [ -z "$2" ]; then
                log_error "Platform not specified"
                show_usage
                exit 1
            fi
            check_prerequisites
            build_image
            test_application
            deploy $2
            ;;
        "check")
            check_prerequisites
            ;;
        "help"|"--help"|"-h")
            show_usage
            ;;
        *)
            log_error "Unknown command: $1"
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"
