# Dubai Real Estate ETL Platform - Docker Compose for Development
version: '3.8'

services:
  # Main ETL Pipeline Service
  dld-etl:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dld-etl-pipeline
    environment:
      # API Configuration
      - DLD_API_CONSUMER_ID=${DLD_API_CONSUMER_ID:-gkb3WvEG0rY9eilwXC0P2pTz8UzvLj9F}
      
      # Data Storage Configuration
      - RAW_DATA_DIR=/app/data/raw
      - PROCESSED_DATA_DIR=/app/data/processed
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      
      # Processing Configuration
      - BATCH_SIZE=${BATCH_SIZE:-1000}
      - PARALLEL_PROCESSING=${PARALLEL_PROCESSING:-true}
      - MAX_RETRIES=${MAX_RETRIES:-3}
      
      # Cloud Storage (optional)
      - CLOUD_STORAGE_BUCKET=${CLOUD_STORAGE_BUCKET:-}
      - CLOUD_PROVIDER=${CLOUD_PROVIDER:-local}
    volumes:
      # Mount data directories for persistence
      - ./data:/app/data
      - ./logs:/app/logs
      - ./visualizations:/app/visualizations
      - ./analysis_results:/app/analysis_results
    ports:
      - "8080:8080"  # For health checks and monitoring
    networks:
      - dld-network
    restart: unless-stopped
    command: >
      sh -c "echo 'DLD ETL Pipeline Ready. Use docker exec to run commands.' && 
             tail -f /dev/null"

  # Jupyter Notebook Service for Analysis
  jupyter:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: dld-jupyter
    environment:
      - JUPYTER_ENABLE_LAB=yes
      - JUPYTER_TOKEN=${JUPYTER_TOKEN:-dld-analysis-token}
    volumes:
      - ./data:/app/data
      - ./notebooks:/app/notebooks
      - ./src:/app/src
      - ./visualizations:/app/visualizations
    ports:
      - "8888:8888"
    networks:
      - dld-network
    restart: unless-stopped
    command: >
      sh -c "pip install jupyterlab && 
             jupyter lab --ip=0.0.0.0 --port=8888 --no-browser --allow-root 
             --NotebookApp.token=${JUPYTER_TOKEN:-dld-analysis-token}"

  # PostgreSQL Database (optional for data warehouse)
  postgres:
    image: postgres:13
    container_name: dld-postgres
    environment:
      - POSTGRES_DB=${POSTGRES_DB:-dld_warehouse}
      - POSTGRES_USER=${POSTGRES_USER:-dld_user}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-dld_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - dld-network
    restart: unless-stopped

  # Redis for Caching (optional)
  redis:
    image: redis:6-alpine
    container_name: dld-redis
    ports:
      - "6379:6379"
    networks:
      - dld-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

  # Monitoring with Prometheus (optional)
  prometheus:
    image: prom/prometheus:latest
    container_name: dld-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    networks:
      - dld-network
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'

  # Grafana for Visualization (optional)
  grafana:
    image: grafana/grafana:latest
    container_name: dld-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana:/etc/grafana/provisioning
    networks:
      - dld-network
    restart: unless-stopped

networks:
  dld-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:
