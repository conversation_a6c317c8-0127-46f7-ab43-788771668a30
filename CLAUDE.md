# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Dubai Real Estate Investment Analysis platform that extracts, transforms, and analyzes property transaction and rental data from the Dubai Land Department (DLD) APIs to identify investment opportunities.

## Architecture

### Core Components

**ETL Pipeline (`src/etl/`)**
- `DLDRentalETL`: Extracts rental contracts from DLD API
- `DLDTransactionETL`: Extracts property transactions from DLD API
- `api_extractor.py`: Base API extraction utilities
- `etl.py`: Legacy ETL pipeline (deprecated)

**Data Processing (`src/`)**
- `analysis.py`: Investment opportunity analysis with rental yield calculations
- `visualizations.py`: Chart generation and dashboard creation
- `projects_converter/`: JSON-to-CSV/GeoJSON converter for project data

**Data Flow**
```
DLD API → Raw JSON → Cleaned CSV → Analysis → Investment Insights
```

### Key Data Structures

**Transaction Data**
- `INSTANCE_DATE`: Transaction date
- `TRANS_VALUE`: Transaction amount (AED)
- `PRICE_PER_SQM`: Price per square meter
- `AREA_EN`: Area name (e.g., Business Bay, Dubai Marina)
- `PROP_TYPE_EN`: Property type (Residential, Commercial, etc.)

**Rental Data**
- `REGISTRATION_DATE`: Contract registration date
- `ANNUAL_AMOUNT`: Annual rent (AED)
- `RENT_PER_SQM`: Rent per square meter
- `START_DATE`, `END_DATE`: Contract duration

## Common Commands

### Data Extraction

**Single Date Range**
```bash
python extract_dld_data.py --from-date "07/14/2025" --to-date "07/15/2025" --data-type all
python extract_rental_data.py --from-date "07/14/2025" --to-date "07/15/2025"
python extract_transaction_data.py --from-date "07/14/2025" --to-date "07/15/2025"
```

**Parallel Processing**
```bash
python extract_dld_data.py --from-date "07/01/2025" --to-date "07/31/2025" --parallel
python run_parallel_etl.py --from-date "07/01/2025" --to-date "07/31/2025" --batch-size 7
```

### Testing
```bash
python test_etl_pipelines.py --test-type both --from-date "07/15/2025" --to-date "07/16/2025"
python -m pytest tests/ -v
```

### Analysis
```bash
python src/analysis.py                    # Run complete market analysis
python src/visualizations.py              # Generate charts and visualizations
jupyter notebook notebooks/               # Interactive analysis
```

### Project Data Processing
```bash
python convert_projects.py                # Convert projects JSON to CSV/GeoJSON
python convert_projects_to_csv.py         # Quick CSV conversion
```

## File Structure

```
data/
├── raw/
│   ├── Registrations/        # Transaction JSON files
│   ├── Rentals/             # Rental JSON files
│   └── projects/            # Project JSON files (daily snapshots)
└── processed/
    ├── transactions.csv     # Cleaned transaction data
    ├── rentals.csv         # Cleaned rental data
    ├── projects.csv        # Processed project data
    └── *summary.csv        # Area/project summaries

src/
├── etl/
├── projects_converter/
├── analysis.py
└── visualizations.py

notebooks/
├── 01_Data_Exploration.ipynb
├── 02_Investor_Dashboard.ipynb
└── 03_Executive_Summary.ipynb
```

## Investment Analysis Methodology

**Scoring System**
- 70% Rental Yield Score: `(rent_per_sqm / price_per_sqm) * 100`
- 30% Activity Score: Normalized transaction + rental count
- Areas filtered for minimum 2 transactions + 2 rentals

**Key Metrics**
- **Rental Yield**: Annual rent / property price
- **Price/Sqm**: Transaction value / actual area
- **Activity Score**: Combined transaction and rental volume

## Development Workflow

### Adding New Analysis
1. Extend `DLDMarketAnalyzer` class in `analysis.py`
2. Add new methods for specific metrics
3. Update `run_complete_analysis()` to include new analysis
4. Add corresponding tests in `tests/`

### ETL Pipeline Extension
1. Modify `src/etl/[rental|transaction]_etl.py` for API changes
2. Update transformation logic in `transform_*` methods
3. Add new columns to summary generation methods
4. Test with `test_etl_pipelines.py`

### Testing Strategy
- **Unit Tests**: `tests/test_*.py` for individual components
- **Integration Tests**: `test_etl_pipelines.py` for full pipeline
- **Data Validation**: Check CSV outputs and summary statistics

## Environment Setup

```bash
# Install dependencies
pip install -r requirements.txt

# Verify installation
python test_etl_pipelines.py --test-type both

# Run sample analysis
python extract_dld_data.py --from-date "07/14/2025" --to-date "07/15/2025"
python src/analysis.py
```

## Common Issues & Solutions

**API Rate Limits**: Use `--batch-size` parameter with parallel processing
**Memory Issues**: Process smaller date ranges (1-7 days at a time)
**Missing Data**: Check `data/raw/` for JSON files and verify date formats
**Analysis Errors**: Ensure `data/processed/` contains both `transactions.csv` and `rentals.csv`

## Date Formats

- **Input**: "MM/DD/YYYY" (e.g., "07/14/2025")
- **Output Files**: YYYY-MM-DD format in filenames
- **API**: Requires different start/end dates (auto-adjusts if same)