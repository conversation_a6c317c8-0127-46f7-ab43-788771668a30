# Google Cloud Run Deployment Configuration
# Dubai Real Estate ETL Platform

apiVersion: serving.knative.dev/v1
kind: Service
metadata:
  name: dld-etl-pipeline
  namespace: default
  labels:
    app: dld-etl
    version: v1.0.0
    environment: production
  annotations:
    run.googleapis.com/ingress: all
    run.googleapis.com/execution-environment: gen2
spec:
  template:
    metadata:
      labels:
        app: dld-etl
        version: v1.0.0
      annotations:
        # Scaling Configuration
        autoscaling.knative.dev/minScale: "0"
        autoscaling.knative.dev/maxScale: "10"
        
        # Resource Configuration
        run.googleapis.com/memory: "4Gi"
        run.googleapis.com/cpu: "2"
        
        # Timeout Configuration
        run.googleapis.com/timeout: "3600s"
        
        # Concurrency Configuration
        run.googleapis.com/execution-environment: gen2
        autoscaling.knative.dev/maxScale: "10"
        
        # VPC Configuration (if needed)
        # run.googleapis.com/vpc-access-connector: projects/PROJECT_ID/locations/REGION/connectors/CONNECTOR_NAME
        
        # Cloud SQL Configuration (if needed)
        # run.googleapis.com/cloudsql-instances: PROJECT_ID:REGION:INSTANCE_NAME
    spec:
      containerConcurrency: 1
      timeoutSeconds: 3600
      serviceAccountName: dld-etl-service-account
      containers:
      - name: dld-etl
        image: gcr.io/PROJECT_ID/dld-etl:latest
        ports:
        - name: http1
          containerPort: 8080
        
        # Environment Variables
        env:
        # API Configuration
        - name: DLD_API_CONSUMER_ID
          valueFrom:
            secretKeyRef:
              name: dld-secrets
              key: api-consumer-id
        
        # Cloud Storage Configuration
        - name: CLOUD_PROVIDER
          value: "gcp"
        - name: GCS_BUCKET
          value: "dld-data-bucket"
        - name: RAW_DATA_DIR
          value: "gs://dld-data-bucket/raw"
        - name: PROCESSED_DATA_DIR
          value: "gs://dld-data-bucket/processed"
        
        # Processing Configuration
        - name: LOG_LEVEL
          value: "INFO"
        - name: BATCH_SIZE
          value: "1000"
        - name: PARALLEL_PROCESSING
          value: "true"
        - name: MAX_RETRIES
          value: "3"
        
        # Monitoring Configuration
        - name: ENABLE_METRICS
          value: "true"
        - name: METRICS_PORT
          value: "8080"
        
        # Google Cloud Configuration
        - name: GOOGLE_CLOUD_PROJECT
          value: "PROJECT_ID"
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/var/secrets/google/key.json"
        
        # Resource Configuration
        resources:
          requests:
            memory: "2Gi"
            cpu: "1"
          limits:
            memory: "4Gi"
            cpu: "2"
        
        # Health Checks
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 10
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        
        # Volume Mounts for Secrets
        volumeMounts:
        - name: google-cloud-key
          mountPath: /var/secrets/google
          readOnly: true
      
      # Volumes
      volumes:
      - name: google-cloud-key
        secret:
          secretName: google-cloud-key

---
# Service Account for Cloud Run
apiVersion: v1
kind: ServiceAccount
metadata:
  name: dld-etl-service-account
  annotations:
    iam.gke.io/gcp-service-account: dld-etl@PROJECT_ID.iam.gserviceaccount.com

---
# Secret for API Keys
apiVersion: v1
kind: Secret
metadata:
  name: dld-secrets
type: Opaque
data:
  # Base64 encoded values
  api-consumer-id: Z2tiM1d2RUcwclk5ZWlsd1hDMFAycFR6OFV6dkxqOUY=  # Replace with actual encoded value

---
# Secret for Google Cloud Service Account Key
apiVersion: v1
kind: Secret
metadata:
  name: google-cloud-key
type: Opaque
data:
  key.json: |
    # Base64 encoded service account key JSON
    # Replace with actual encoded service account key

---
# Horizontal Pod Autoscaler (if using GKE)
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: dld-etl-hpa
spec:
  scaleTargetRef:
    apiVersion: serving.knative.dev/v1
    kind: Service
    name: dld-etl-pipeline
  minReplicas: 0
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80

---
# Cloud Scheduler Job for Daily ETL
apiVersion: v1
kind: ConfigMap
metadata:
  name: etl-scheduler-config
data:
  schedule: "0 2 * * *"  # Daily at 2 AM UTC
  timezone: "UTC"
  job-name: "daily-dld-etl"
  target-url: "https://dld-etl-pipeline-PROJECT_ID.a.run.app/trigger-etl"
  http-method: "POST"
  payload: |
    {
      "from_date": "{{ ds }}",
      "to_date": "{{ next_ds }}",
      "data_type": "all",
      "parallel": true
    }
